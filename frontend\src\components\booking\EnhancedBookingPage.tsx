import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { FiCalendar, FiClock, FiUser, FiMail, FiPhone, FiCheck, FiArrowLeft, FiArrowRight } from 'react-icons/fi';
import { api } from '../../utils/api';

interface BookingPageData {
  tenant: {
    id: string;
    name: string;
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
    welcomeMessage?: string;
    currency?: string;
    timezone?: string;
  };
  settings: {
    title: string;
    description?: string;
    primary_color: string;
    show_logo: boolean;
    require_phone: boolean;
    require_address: boolean;
    show_prices: boolean;
    show_duration: boolean;
    custom_confirmation_message?: string;
  };
  services: Array<{
    id: string;
    name: string;
    description?: string;
    duration: number;
    price: number;
    color?: string;
  }>;
  staff: Array<{
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    avatar_url?: string;
    bio?: string;
    serviceIds: string[];
  }>;
}

interface TimeSlot {
  time: string;
  available: boolean;
}

interface ClientInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
}

const EnhancedBookingPage: React.FC = () => {
  const { tenantId } = useParams<{ tenantId: string }>();
  
  // State management
  const [bookingData, setBookingData] = useState<BookingPageData | null>(null);
  const [selectedService, setSelectedService] = useState<string>('');
  const [selectedStaff, setSelectedStaff] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [availableTimeSlots, setAvailableTimeSlots] = useState<TimeSlot[]>([]);
  const [clientInfo, setClientInfo] = useState<ClientInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: ''
  });
  
  // UI state
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [bookingSuccess, setBookingSuccess] = useState(false);

  // Fetch booking page data
  useEffect(() => {
    const fetchBookingData = async () => {
      if (!tenantId) return;

      try {
        setIsLoading(true);
        setError(null);

        const response = await api.get(`/booking-pages/${tenantId}/public`);
        
        if (response.data.success) {
          setBookingData(response.data.data);
        } else {
          setError(response.data.message || 'Failed to load booking page');
        }
      } catch (err: any) {
        console.error('Error fetching booking data:', err);
        if (err.response?.status === 403) {
          setError('This business is not currently accepting bookings.');
        } else if (err.response?.status === 404) {
          setError('Booking page not found.');
        } else {
          setError('Failed to load booking page. Please try again later.');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookingData();
  }, [tenantId]);

  // Fetch available time slots
  useEffect(() => {
    const fetchTimeSlots = async () => {
      if (!tenantId || !selectedService || !selectedStaff || !selectedDate) {
        setAvailableTimeSlots([]);
        return;
      }

      try {
        const response = await api.get(`/appointments/${tenantId}/timeslots`, {
          params: {
            serviceId: selectedService,
            staffId: selectedStaff,
            date: selectedDate
          }
        });

        setAvailableTimeSlots(response.data.timeSlots || []);
        
        // Clear selected time if it's no longer available
        if (selectedTime && !response.data.timeSlots?.some((slot: TimeSlot) => slot.time === selectedTime)) {
          setSelectedTime('');
        }
      } catch (err: any) {
        console.error('Error fetching time slots:', err);
        setAvailableTimeSlots([]);
      }
    };

    fetchTimeSlots();
  }, [tenantId, selectedService, selectedStaff, selectedDate, selectedTime]);

  // Handle form submission
  const handleBookingSubmit = async () => {
    if (!tenantId || !selectedService || !selectedStaff || !selectedDate || !selectedTime) {
      setError('Please complete all required fields');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Create appointment
      const appointmentData = {
        clientId: null, // Will be created by the backend
        staffId: selectedStaff,
        serviceId: selectedService,
        startTime: `${selectedDate}T${selectedTime}`,
        notes: 'Booked through public booking page',
        clientInfo: {
          firstName: clientInfo.firstName,
          lastName: clientInfo.lastName,
          email: clientInfo.email,
          phone: clientInfo.phone,
          address: clientInfo.address
        }
      };

      await api.post(`/appointments/${tenantId}/appointments`, appointmentData);
      setBookingSuccess(true);

    } catch (err: any) {
      console.error('Error booking appointment:', err);
      setError(err.response?.data?.message || 'Failed to book appointment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper functions
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const formatPrice = (price: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const getSelectedService = () => {
    return bookingData?.services.find(service => service.id === selectedService);
  };

  const getSelectedStaff = () => {
    return bookingData?.staff.find(staff => staff.id === selectedStaff);
  };

  const getAvailableStaff = () => {
    if (!selectedService || !bookingData) return [];
    return bookingData.staff.filter(staff => 
      staff.serviceIds.includes(selectedService)
    );
  };

  // Custom styles based on tenant branding
  const primaryColor = bookingData?.settings.primary_color || bookingData?.tenant.primaryColor || '#3b82f6';
  const customStyles = {
    '--primary-color': primaryColor,
    '--primary-color-dark': primaryColor + 'dd',
    '--primary-color-light': primaryColor + '20'
  } as React.CSSProperties;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading booking page...</p>
        </div>
      </div>
    );
  }

  if (error && !bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (bookingSuccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-6">
          <div className="bg-green-100 border border-green-400 text-green-700 px-6 py-8 rounded-lg">
            <FiCheck className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">Booking Confirmed!</h2>
            <p className="mb-4">
              {bookingData?.settings.custom_confirmation_message || 
               `Thank you for booking with ${bookingData?.tenant.name}. You will receive a confirmation email shortly.`}
            </p>
            <button
              onClick={() => {
                setBookingSuccess(false);
                setCurrentStep(1);
                setSelectedService('');
                setSelectedStaff('');
                setSelectedTime('');
                setClientInfo({
                  firstName: '',
                  lastName: '',
                  email: '',
                  phone: '',
                  address: ''
                });
              }}
              style={{ backgroundColor: primaryColor }}
              className="text-white px-6 py-2 rounded-md hover:opacity-90 transition-opacity"
            >
              Book Another Appointment
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50" style={customStyles}>
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center">
            {bookingData?.settings.show_logo && bookingData?.tenant.logo && (
              <img
                src={bookingData.tenant.logo}
                alt={bookingData.tenant.name}
                className="h-16 mx-auto mb-4"
              />
            )}
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {bookingData?.settings.title || `Book with ${bookingData?.tenant.name}`}
            </h1>
            {bookingData?.settings.description && (
              <p className="text-gray-600 max-w-2xl mx-auto">
                {bookingData.settings.description}
              </p>
            )}
            {bookingData?.tenant.welcomeMessage && (
              <p className="text-gray-600 max-w-2xl mx-auto mt-2">
                {bookingData.tenant.welcomeMessage}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {/* Progress Steps */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="flex">
            {[1, 2, 3].map((step) => (
              <div
                key={step}
                className={`flex-1 text-center py-4 border-b-2 transition-colors ${
                  currentStep === step
                    ? 'border-blue-600 text-blue-600 bg-blue-50'
                    : currentStep > step
                    ? 'border-green-600 text-green-600'
                    : 'border-gray-200 text-gray-500'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      currentStep === step
                        ? 'bg-blue-600 text-white'
                        : currentStep > step
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-200 text-gray-500'
                    }`}
                  >
                    {currentStep > step ? <FiCheck /> : step}
                  </div>
                  <span className="hidden sm:inline font-medium">
                    {step === 1 && 'Select Service'}
                    {step === 2 && 'Your Details'}
                    {step === 3 && 'Confirm'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          {/* Step 1: Service Selection */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Choose Your Service
              </h2>

              {/* Service Selection */}
              <div className="grid gap-4 md:grid-cols-2">
                {bookingData?.services.map((service) => (
                  <div
                    key={service.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedService === service.id
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedService(service.id);
                      setSelectedStaff('');
                      setSelectedTime('');
                    }}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium text-gray-900">{service.name}</h3>
                      {bookingData?.settings.show_prices && (
                        <span className="text-lg font-semibold text-gray-900">
                          {formatPrice(service.price, bookingData?.tenant.currency)}
                        </span>
                      )}
                    </div>
                    {service.description && (
                      <p className="text-gray-600 text-sm mb-2">{service.description}</p>
                    )}
                    {bookingData?.settings.show_duration && (
                      <div className="flex items-center text-sm text-gray-500">
                        <FiClock className="h-4 w-4 mr-1" />
                        {service.duration} minutes
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Staff Selection */}
              {selectedService && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Choose Your Provider
                  </h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    {getAvailableStaff().map((staff) => (
                      <div
                        key={staff.id}
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          selectedStaff === staff.id
                            ? 'border-blue-600 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => {
                          setSelectedStaff(staff.id);
                          setSelectedTime('');
                        }}
                      >
                        <div className="flex items-center space-x-3">
                          {staff.avatar_url ? (
                            <img
                              src={staff.avatar_url}
                              alt={`${staff.first_name} ${staff.last_name}`}
                              className="h-12 w-12 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                              <FiUser className="h-6 w-6 text-gray-500" />
                            </div>
                          )}
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {staff.first_name} {staff.last_name}
                            </h4>
                            {staff.bio && (
                              <p className="text-sm text-gray-600">{staff.bio}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Date Selection */}
              {selectedStaff && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Select Date
                  </h3>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => {
                      setSelectedDate(e.target.value);
                      setSelectedTime('');
                    }}
                    min={new Date().toISOString().split('T')[0]}
                    max={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                      .toISOString()
                      .split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              {/* Time Selection */}
              {selectedDate && selectedStaff && selectedService && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Available Times
                  </h3>
                  {availableTimeSlots.length > 0 ? (
                    <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
                      {availableTimeSlots.map((slot) => (
                        <button
                          key={slot.time}
                          type="button"
                          disabled={!slot.available}
                          className={`py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                            selectedTime === slot.time
                              ? 'bg-blue-600 text-white'
                              : slot.available
                              ? 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                              : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                          }`}
                          onClick={() => setSelectedTime(slot.time)}
                        >
                          {formatTime(slot.time)}
                        </button>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <FiCalendar className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                      <p>No available time slots for the selected date.</p>
                      <p className="text-sm">Please choose a different date.</p>
                    </div>
                  )}
                </div>
              )}

              {/* Next Button */}
              <div className="flex justify-end pt-4">
                <button
                  type="button"
                  onClick={() => {
                    if (!selectedService || !selectedStaff || !selectedDate || !selectedTime) {
                      setError('Please complete all selections before continuing');
                      return;
                    }
                    setError(null);
                    setCurrentStep(2);
                  }}
                  disabled={!selectedService || !selectedStaff || !selectedDate || !selectedTime}
                  style={{ backgroundColor: primaryColor }}
                  className="px-6 py-2 text-white rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <span>Continue</span>
                  <FiArrowRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Client Information */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Your Information
              </h2>

              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <div className="relative">
                    <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      id="firstName"
                      value={clientInfo.firstName}
                      onChange={(e) => setClientInfo(prev => ({ ...prev, firstName: e.target.value }))}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <div className="relative">
                    <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      id="lastName"
                      value={clientInfo.lastName}
                      onChange={(e) => setClientInfo(prev => ({ ...prev, lastName: e.target.value }))}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <div className="relative">
                    <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="email"
                      id="email"
                      value={clientInfo.email}
                      onChange={(e) => setClientInfo(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number {bookingData?.settings.require_phone ? '*' : '(Optional)'}
                  </label>
                  <div className="relative">
                    <FiPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="tel"
                      id="phone"
                      value={clientInfo.phone}
                      onChange={(e) => setClientInfo(prev => ({ ...prev, phone: e.target.value }))}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required={bookingData?.settings.require_phone}
                    />
                  </div>
                </div>

                {bookingData?.settings.require_address && (
                  <div className="md:col-span-2">
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                      Address *
                    </label>
                    <textarea
                      id="address"
                      value={clientInfo.address}
                      onChange={(e) => setClientInfo(prev => ({ ...prev, address: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                )}
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-4">
                <button
                  type="button"
                  onClick={() => setCurrentStep(1)}
                  className="px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors flex items-center space-x-2"
                >
                  <FiArrowLeft className="h-4 w-4" />
                  <span>Back</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    const requiredFields = ['firstName', 'lastName', 'email'];
                    if (bookingData?.settings.require_phone) requiredFields.push('phone');
                    if (bookingData?.settings.require_address) requiredFields.push('address');

                    const missingFields = requiredFields.filter(field =>
                      !clientInfo[field as keyof ClientInfo]?.trim()
                    );

                    if (missingFields.length > 0) {
                      setError('Please fill in all required fields');
                      return;
                    }

                    setError(null);
                    setCurrentStep(3);
                  }}
                  style={{ backgroundColor: primaryColor }}
                  className="px-6 py-2 text-white rounded-md hover:opacity-90 transition-opacity flex items-center space-x-2"
                >
                  <span>Continue</span>
                  <FiArrowRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Confirmation */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Confirm Your Booking
              </h2>

              {/* Appointment Summary */}
              <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Appointment Details
                </h3>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FiCalendar className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Service</p>
                      <p className="font-medium text-gray-900">{getSelectedService()?.name}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <FiUser className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Provider</p>
                      <p className="font-medium text-gray-900">
                        {getSelectedStaff()?.first_name} {getSelectedStaff()?.last_name}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <FiCalendar className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Date</p>
                      <p className="font-medium text-gray-900">
                        {new Date(selectedDate).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      <FiClock className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Time</p>
                      <p className="font-medium text-gray-900">{formatTime(selectedTime)}</p>
                    </div>
                  </div>

                  {bookingData?.settings.show_duration && (
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <FiClock className="h-5 w-5 text-indigo-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Duration</p>
                        <p className="font-medium text-gray-900">{getSelectedService()?.duration} minutes</p>
                      </div>
                    </div>
                  )}

                  {bookingData?.settings.show_prices && (
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <span className="text-yellow-600 font-bold">$</span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Price</p>
                        <p className="font-medium text-gray-900">
                          {formatPrice(getSelectedService()?.price || 0, bookingData?.tenant.currency)}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Client Information Summary */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Your Information
                </h3>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p className="font-medium text-gray-900">
                      {clientInfo.firstName} {clientInfo.lastName}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium text-gray-900">{clientInfo.email}</p>
                  </div>

                  {clientInfo.phone && (
                    <div>
                      <p className="text-sm text-gray-500">Phone</p>
                      <p className="font-medium text-gray-900">{clientInfo.phone}</p>
                    </div>
                  )}

                  {clientInfo.address && (
                    <div className="md:col-span-2">
                      <p className="text-sm text-gray-500">Address</p>
                      <p className="font-medium text-gray-900">{clientInfo.address}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-4">
                <button
                  type="button"
                  onClick={() => setCurrentStep(2)}
                  className="px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors flex items-center space-x-2"
                >
                  <FiArrowLeft className="h-4 w-4" />
                  <span>Back</span>
                </button>
                <button
                  type="button"
                  onClick={handleBookingSubmit}
                  disabled={isSubmitting}
                  style={{ backgroundColor: primaryColor }}
                  className="px-8 py-2 text-white rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Booking...</span>
                    </>
                  ) : (
                    <>
                      <FiCheck className="h-4 w-4" />
                      <span>Confirm Booking</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedBookingPage;
