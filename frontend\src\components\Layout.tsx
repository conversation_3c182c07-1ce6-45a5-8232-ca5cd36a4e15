import React, { ReactNode, useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../services/userService';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, tenant, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="bg-blue-600 text-white shadow-md">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link to="/" className="text-xl font-bold">
            {tenant ? tenant.name : 'Scheduly'}
          </Link>

          <nav className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                {/* Common links for authenticated users (except Super Admin) */}
                {user && user.role !== UserRole.SUPER_ADMIN && (
                  <Link to="/pricing" className="hover:text-blue-200">
                    Pricing
                  </Link>
                )}

                {/* Super Admin Navigation */}
                {user && user.role === UserRole.SUPER_ADMIN && (
                  <>
                    <Link to="/super-admin/dashboard" className="hover:text-blue-200">
                      Dashboard
                    </Link>
                    <Link to="/super-admin/businesses" className="hover:text-blue-200">
                      Businesses
                    </Link>
                    <Link to="/admin/subscription" className="hover:text-blue-200">
                      Subscriptions
                    </Link>
                  </>
                )}

                {/* Admin and Staff Navigation */}
                {user && (user.role === UserRole.ADMIN || user.role === UserRole.STAFF) && (
                  <>
                    <Link to="/dashboard" className="hover:text-blue-200">
                      Dashboard
                    </Link>
                    <Link to="/appointments" className="hover:text-blue-200">
                      Appointments
                    </Link>
                    {user.role === UserRole.ADMIN && (
                      <>
                        <Link to="/services" className="hover:text-blue-200">
                          Services
                        </Link>
                        <Link to="/staff" className="hover:text-blue-200">
                          Staff
                        </Link>
                        <Link to="/clients" className="hover:text-blue-200">
                          Clients
                        </Link>
                        <Link to="/analytics" className="hover:text-blue-200">
                          Analytics
                        </Link>
                        <Link to="/booking-page-settings" className="hover:text-blue-200">
                          Booking Page
                        </Link>
                        <Link to="/settings" className="hover:text-blue-200">
                          Settings
                        </Link>
                      </>
                    )}
                  </>
                )}

                {/* Client Navigation */}
                {user && user.role === UserRole.CLIENT && (
                  <>
                    <Link to="/my-appointments" className="hover:text-blue-200">
                      My Appointments
                    </Link>
                    <Link to="/book" className="hover:text-blue-200">
                      Book Appointment
                    </Link>
                  </>
                )}

                {/* User Menu */}
                <div className="relative" ref={dropdownRef}>
                  <button
                    className="flex items-center hover:text-blue-200"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <span>{user?.firstName}</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-5 w-5 ml-1 transition-transform duration-200 ${isDropdownOpen ? 'transform rotate-180' : ''}`}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                  {isDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsDropdownOpen(false)}
                      >
                        Profile
                      </Link>
                      {/* Hide Subscription link for Super Admin */}
                      {user?.role !== UserRole.SUPER_ADMIN && (
                        <Link
                          to="/subscription"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setIsDropdownOpen(false)}
                        >
                          Subscription
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          setIsDropdownOpen(false);
                          handleLogout();
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <>
                <Link to="/pricing" className="hover:text-blue-200">
                  Pricing
                </Link>
                <Link
                  to="/login"
                  className="bg-white text-blue-600 px-4 py-2 rounded-md hover:bg-blue-50"
                >
                  Login
                </Link>
              </>
            )}
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow container mx-auto px-4 py-8">{children}</main>

      {/* Footer */}
      <footer className="bg-gray-100 py-6">
        <div className="container mx-auto px-4 text-center text-gray-600">
          <p>&copy; {new Date().getFullYear()} Scheduly. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
