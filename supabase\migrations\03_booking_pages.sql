-- Create booking_pages table for tenant booking page customization
CREATE TABLE IF NOT EXISTS booking_pages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    title TEXT NOT NULL DEFAULT 'Book an Appointment',
    description TEXT DEFAULT 'Select a service and time to book your appointment.',
    primary_color TEXT NOT NULL DEFAULT '#3b82f6',
    show_logo BOOLEAN NOT NULL DEFAULT true,
    custom_css TEXT DEFAULT '',
    custom_js TEXT DEFAULT '',
    custom_confirmation_message TEXT DEFAULT '',
    require_phone BOOLEAN NOT NULL DEFAULT true,
    require_address BOOLEAN NOT NULL DEFAULT false,
    show_prices BOOLEAN NOT NULL DEFAULT true,
    show_duration BOOLEAN NOT NULL DEFAULT true,
    allow_cancellation BOOLEAN NOT NULL DEFAULT true,
    cancellation_period_hours INTEGER NOT NULL DEFAULT 24,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one booking page per tenant
    UNIQUE(tenant_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_booking_pages_tenant_id ON booking_pages(tenant_id);

-- Enable RLS
ALTER TABLE booking_pages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for booking_pages
-- Allow public read access for booking page data
CREATE POLICY "Allow public read access to booking pages" ON booking_pages
    FOR SELECT USING (true);

-- Allow tenant admins to manage their booking page
CREATE POLICY "Allow tenant admins to manage booking pages" ON booking_pages
    FOR ALL USING (
        tenant_id IN (
            SELECT tenant_id FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Allow super admins to manage all booking pages
CREATE POLICY "Allow super admins to manage all booking pages" ON booking_pages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'super_admin'
        )
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_booking_pages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_booking_pages_updated_at
    BEFORE UPDATE ON booking_pages
    FOR EACH ROW
    EXECUTE FUNCTION update_booking_pages_updated_at();
