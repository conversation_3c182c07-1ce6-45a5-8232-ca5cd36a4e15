import express from 'express';
import {
  getPublicBookingPageData,
  getTenantBookingPageSettings,
  updateTenantBookingPageSettings,
  getBookingPageUrl
} from '../controllers/bookingPageController';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '../models/supabase';
import { asyncHandler } from '../utils/routeHandler';

const router = express.Router();

// Public routes
router.get('/:tenantId/public', asyncHandler(getPublicBookingPageData));

// Protected routes - require authentication
router.get('/:tenantId/settings', authenticate, asyncHandler(getTenantBookingPageSettings));
router.put('/:tenantId/settings', authenticate, authorize(UserRole.ADMIN), asyncHandler(updateTenantBookingPageSettings));
router.get('/:tenantId/url', authenticate, asyncHandler(getBookingPageUrl));

export default router;
