import React from 'react';
import EnhancedBookingPage from '../components/booking/EnhancedBookingPage';

const BookingPage: React.FC = () => {
  return <EnhancedBookingPage />;
};

export default BookingPage;
        setIsLoading(true);
        setError(null);

        const tenantData = await tenantService.getTenantBookingPage(tenantId);
        setTenant(tenantData);

        // Fetch services
        const servicesData = await serviceService.getServices(tenantId);
        setServices(servicesData.filter((service: any) => service.active));

        // Fetch staff
        const staffData = await tenantService.getTenantStaff(tenantId);
        setStaff(staffData.filter((user: any) => user.active));

      } catch (err: any) {
        console.error('Error fetching tenant details:', err);
        setError('Failed to load booking page. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTenantDetails();
  }, [tenantId]);

  // Fetch available time slots when service, staff, and date are selected
  useEffect(() => {
    const fetchTimeSlots = async () => {
      if (!tenantId || !selectedService || !selectedStaff || !selectedDate) return;

      try {
        setIsLoading(true);

        const timeSlotsData = await appointmentService.getAvailableTimeSlots(
          tenantId,
          {
            serviceId: selectedService,
            staffId: selectedStaff,
            date: selectedDate
          }
        );

        setAvailableTimeSlots(timeSlotsData.timeSlots);

        // Clear selected time if it's no longer available
        if (selectedTime && !timeSlotsData.timeSlots.some(slot => slot.time === selectedTime)) {
          setSelectedTime('');
        }

      } catch (err: any) {
        console.error('Error fetching time slots:', err);
        setError('Failed to load available time slots. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    if (selectedService && selectedStaff && selectedDate) {
      fetchTimeSlots();
    } else {
      setAvailableTimeSlots([]);
      setSelectedTime('');
    }
  }, [tenantId, selectedService, selectedStaff, selectedDate]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setClientInfo(prev => ({ ...prev, [name]: value }));
  };

  // Handle service selection
  const handleServiceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedService(e.target.value);
    setSelectedStaff('');
    setSelectedTime('');
  };

  // Handle staff selection
  const handleStaffChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStaff(e.target.value);
    setSelectedTime('');
  };

  // Handle date selection
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedDate(e.target.value);
    setSelectedTime('');
  };

  // Handle time slot selection
  const handleTimeSlotSelect = (time: string) => {
    setSelectedTime(time);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tenantId || !selectedService || !selectedStaff || !selectedDate || !selectedTime) {
      setError('Please complete all required fields');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // First, register the client if they don't have an account
      let clientId;

      try {
        // Try to register the client
        const clientData = await authService.registerClient({
          tenantId,
          firstName: clientInfo.firstName,
          lastName: clientInfo.lastName,
          email: clientInfo.email,
          phone: clientInfo.phone,
          password: Math.random().toString(36).substring(2, 10) // Generate a random password
        });

        clientId = clientData.id;
      } catch (err: any) {
        // If client already exists, try to get their ID
        if (err.response && err.response.status === 400 && err.response.data.message.includes('already exists')) {
          // Get client ID by email
          const clients = await userService.getUsers(tenantId);
          const existingClient = clients.find((client: any) => client.email === clientInfo.email);

          if (existingClient) {
            clientId = existingClient.id;
          } else {
            throw new Error('Failed to find existing client');
          }
        } else {
          throw err;
        }
      }

      // Create the appointment
      const appointmentData = {
        clientId,
        staffId: selectedStaff,
        serviceId: selectedService,
        startTime: `${selectedDate}T${selectedTime}`,
        notes: 'Booked through public booking page'
      };

      await appointmentService.createAppointment(tenantId, appointmentData);

      // Show success message
      setBookingSuccess(true);

    } catch (err: any) {
      console.error('Error booking appointment:', err);
      setError('Failed to book appointment. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle next step
  const handleNextStep = () => {
    if (step === 1 && (!selectedService || !selectedStaff || !selectedDate || !selectedTime)) {
      setError('Please complete all required fields');
      return;
    }

    if (step === 2 && (!clientInfo.firstName || !clientInfo.lastName || !clientInfo.email)) {
      setError('Please complete all required fields');
      return;
    }

    setError(null);
    setStep(step + 1);
  };

  // Handle previous step
  const handlePrevStep = () => {
    setError(null);
    setStep(step - 1);
  };

  // Format time for display
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  // Get selected service details
  const getSelectedServiceDetails = () => {
    return services.find((service: any) => service.id === selectedService);
  };

  // Get selected staff details
  const getSelectedStaffDetails = () => {
    return staff.find((person: any) => person.id === selectedStaff);
  };

  if (isLoading && !tenant) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }

  if (error && !tenant) {
    return (
      <Layout>
        <div className="max-w-3xl mx-auto text-center py-12">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </Layout>
    );
  }

  if (bookingSuccess) {
    return (
      <Layout>
        <div className="max-w-3xl mx-auto text-center py-12">
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <h2 className="text-2xl font-bold mb-4">Appointment Booked Successfully!</h2>
            <p>
              Thank you for booking with {tenant?.name}. You will receive a confirmation email shortly.
            </p>
          </div>
          <button
            onClick={() => {
              setBookingSuccess(false);
              setStep(1);
              setSelectedService('');
              setSelectedStaff('');
              setSelectedTime('');
              setClientInfo({
                firstName: '',
                lastName: '',
                email: '',
                phone: ''
              });
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Book Another Appointment
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-3xl mx-auto">
        {tenant && (
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{tenant.name}</h1>
            {tenant.welcomeMessage && (
              <p className="text-gray-600">{tenant.welcomeMessage}</p>
            )}
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Progress Steps */}
          <div className="flex border-b">
            <div
              className={`flex-1 text-center py-4 ${
                step === 1 ? 'bg-blue-50 text-blue-600 font-medium' : ''
              }`}
            >
              1. Select Service & Time
            </div>
            <div
              className={`flex-1 text-center py-4 ${
                step === 2 ? 'bg-blue-50 text-blue-600 font-medium' : ''
              }`}
            >
              2. Your Information
            </div>
            <div
              className={`flex-1 text-center py-4 ${
                step === 3 ? 'bg-blue-50 text-blue-600 font-medium' : ''
              }`}
            >
              3. Confirm Booking
            </div>
          </div>

          <div className="p-6">
            {/* Step 1: Select Service & Time */}
            {step === 1 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Select Service & Time</h2>

                <div className="mb-4">
                  <label htmlFor="service" className="block text-gray-700 font-medium mb-2">
                    Service *
                  </label>
                  <select
                    id="service"
                    value={selectedService}
                    onChange={handleServiceChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a service</option>
                    {services.map((service: any) => (
                      <option key={service.id} value={service.id}>
                        {service.name} - ${service.price} ({service.duration} min)
                      </option>
                    ))}
                  </select>
                </div>

                {selectedService && (
                  <div className="mb-4">
                    <label htmlFor="staff" className="block text-gray-700 font-medium mb-2">
                      Staff Member *
                    </label>
                    <select
                      id="staff"
                      value={selectedStaff}
                      onChange={handleStaffChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">Select a staff member</option>
                      {staff
                        .filter((person: any) => {
                          // Only show staff that can provide the selected service
                          const serviceIds = person.services?.map((s: any) => s.id) || [];
                          return serviceIds.includes(selectedService);
                        })
                        .map((person: any) => (
                          <option key={person.id} value={person.id}>
                            {person.firstName} {person.lastName}
                          </option>
                        ))}
                    </select>
                  </div>
                )}

                {selectedStaff && (
                  <div className="mb-4">
                    <label htmlFor="date" className="block text-gray-700 font-medium mb-2">
                      Date *
                    </label>
                    <input
                      type="date"
                      id="date"
                      value={selectedDate}
                      onChange={handleDateChange}
                      min={new Date().toISOString().split('T')[0]}
                      max={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                        .toISOString()
                        .split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                )}

                {selectedDate && selectedStaff && selectedService && (
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">
                      Available Time Slots *
                    </label>
                    {isLoading ? (
                      <div className="flex justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                      </div>
                    ) : availableTimeSlots.length > 0 ? (
                      <div className="grid grid-cols-3 gap-2">
                        {availableTimeSlots.map((time) => (
                          <button
                            key={time}
                            type="button"
                            className={`py-2 px-4 rounded-md text-center ${
                              selectedTime === time
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-100 hover:bg-gray-200'
                            }`}
                            onClick={() => handleTimeSlotSelect(time)}
                          >
                            {formatTime(time)}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-gray-500">
                        No available time slots for the selected date.
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    onClick={handleNextStep}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    disabled={!selectedService || !selectedStaff || !selectedDate || !selectedTime}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Your Information */}
            {step === 2 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Your Information</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="firstName" className="block text-gray-700 font-medium mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={clientInfo.firstName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-gray-700 font-medium mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={clientInfo.lastName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={clientInfo.email}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={clientInfo.phone}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="mt-6 flex justify-between">
                  <button
                    type="button"
                    onClick={handlePrevStep}
                    className="bg-gray-200 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-300"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={handleNextStep}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    disabled={!clientInfo.firstName || !clientInfo.lastName || !clientInfo.email}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}

            {/* Step 3: Confirm Booking */}
            {step === 3 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Confirm Your Booking</h2>

                <div className="bg-gray-50 p-4 rounded-md mb-6">
                  <h3 className="font-medium text-gray-800 mb-2">Appointment Details</h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Service</p>
                      <p className="font-medium">{getSelectedServiceDetails()?.name}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Staff</p>
                      <p className="font-medium">
                        {getSelectedStaffDetails()?.firstName} {getSelectedStaffDetails()?.lastName}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Date</p>
                      <p className="font-medium">
                        {new Date(selectedDate).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Time</p>
                      <p className="font-medium">{formatTime(selectedTime)}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Duration</p>
                      <p className="font-medium">{getSelectedServiceDetails()?.duration} minutes</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Price</p>
                      <p className="font-medium">${getSelectedServiceDetails()?.price}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-md mb-6">
                  <h3 className="font-medium text-gray-800 mb-2">Your Information</h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium">
                        {clientInfo.firstName} {clientInfo.lastName}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{clientInfo.email}</p>
                    </div>

                    {clientInfo.phone && (
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="font-medium">{clientInfo.phone}</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-6 flex justify-between">
                  <button
                    type="button"
                    onClick={handlePrevStep}
                    className="bg-gray-200 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-300"
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    onClick={handleSubmit}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Booking...
                      </span>
                    ) : (
                      'Confirm Booking'
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BookingPage;
