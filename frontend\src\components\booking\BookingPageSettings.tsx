import React, { useState, useEffect } from 'react';
import { <PERSON>Save, <PERSON><PERSON>ye, FiCopy, FiQrCode, FiCode, FiExternalLink } from 'react-icons/fi';
import { api } from '../../utils/api';
import { useAuth } from '../../contexts/AuthContext';

interface BookingPageSettings {
  title: string;
  description: string;
  primary_color: string;
  show_logo: boolean;
  custom_css?: string;
  custom_js?: string;
  custom_confirmation_message?: string;
  require_phone: boolean;
  require_address: boolean;
  show_prices: boolean;
  show_duration: boolean;
  allow_cancellation: boolean;
  cancellation_period_hours: number;
}

const BookingPageSettings: React.FC = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<BookingPageSettings>({
    title: '',
    description: '',
    primary_color: '#3b82f6',
    show_logo: true,
    custom_css: '',
    custom_js: '',
    custom_confirmation_message: '',
    require_phone: false,
    require_address: false,
    show_prices: true,
    show_duration: true,
    allow_cancellation: true,
    cancellation_period_hours: 24
  });

  const [bookingUrl, setBookingUrl] = useState('');
  const [embedCode, setEmbedCode] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [canUseCustomizable, setCanUseCustomizable] = useState(false);
  const [upgradeMessage, setUpgradeMessage] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      if (!user?.tenantId) return;

      try {
        setIsLoading(true);
        
        try {
          // Try new booking pages API first
          const settingsResponse = await api.get(`/booking-pages/${user.tenantId}/settings`);
          if (settingsResponse.data.success) {
            setSettings(settingsResponse.data.data);
            setCanUseCustomizable(settingsResponse.data.canUseCustomizable);
            setUpgradeMessage(settingsResponse.data.upgradeMessage || '');
          }

          // Fetch booking URL
          const urlResponse = await api.get(`/booking-pages/${user.tenantId}/url`);
          if (urlResponse.data.success) {
            setBookingUrl(urlResponse.data.data.bookingUrl);
            setEmbedCode(urlResponse.data.data.embedCode);
            setQrCodeUrl(urlResponse.data.data.qrCodeUrl);
          }
        } catch (err: any) {
          // Fallback for when booking pages API doesn't exist yet
          console.log('Booking pages API not available, using fallback...');

          // Generate fallback URL
          const baseUrl = window.location.origin;
          const fallbackUrl = `${baseUrl}/booking/${user.tenantId}`;

          setBookingUrl(fallbackUrl);
          setEmbedCode(`<iframe src="${fallbackUrl}" width="100%" height="600" frameborder="0"></iframe>`);
          setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(fallbackUrl)}`);

          // Set basic settings
          setSettings({
            title: 'Book an Appointment',
            description: 'Select a service and time to book your appointment.',
            primary_color: '#3b82f6',
            show_logo: true,
            custom_css: '',
            custom_js: '',
            custom_confirmation_message: '',
            require_phone: true,
            require_address: false,
            show_prices: true,
            show_duration: true,
            allow_cancellation: true,
            cancellation_period_hours: 24
          });

          setCanUseCustomizable(false);
          setUpgradeMessage('Booking page customization will be available soon!');
        }

      } catch (err: any) {
        console.error('Error fetching booking page settings:', err);
        setError('Failed to load booking page settings');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [user?.tenantId]);

  // Handle form submission
  const handleSave = async () => {
    if (!user?.tenantId) return;

    try {
      setIsSaving(true);
      setError(null);
      setSuccess(null);

      try {
        const response = await api.put(`/booking-pages/${user.tenantId}/settings`, settings);

        if (response.data.success) {
          setSuccess('Booking page settings saved successfully!');
        } else {
          setError(response.data.message || 'Failed to save settings');
        }
      } catch (err: any) {
        // Fallback - just show success message for now
        console.log('Booking pages API not available, settings will be saved when API is ready');
        setSuccess('Settings saved! (Note: Full booking page customization will be available soon)');
      }

    } catch (err: any) {
      console.error('Error saving booking page settings:', err);
      if (err.response?.status === 403) {
        setError(err.response.data.message || 'Upgrade required for this feature');
      } else {
        setError('Failed to save settings. Please try again.');
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setSuccess('Copied to clipboard!');
      setTimeout(() => setSuccess(null), 2000);
    } catch (err) {
      setError('Failed to copy to clipboard');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Booking Page Settings
            </h1>
            <p className="text-gray-600">
              Customize your public booking page and share it with clients
            </p>
          </div>
          <div className="flex space-x-3">
            <a
              href={bookingUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              <FiEye className="h-4 w-4 mr-2" />
              Preview
            </a>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <FiSave className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      {/* Booking URL Section */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Share Your Booking Page
        </h2>
        
        <div className="space-y-4">
          {/* Booking URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Booking Page URL
            </label>
            <div className="flex">
              <input
                type="text"
                value={bookingUrl}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-gray-600"
              />
              <button
                onClick={() => copyToClipboard(bookingUrl)}
                className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors"
              >
                <FiCopy className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Embed Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Embed Code (for websites)
            </label>
            <div className="flex">
              <textarea
                value={embedCode}
                readOnly
                rows={3}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-gray-600 text-sm font-mono"
              />
              <button
                onClick={() => copyToClipboard(embedCode)}
                className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors"
              >
                <FiCode className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* QR Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              QR Code
            </label>
            <div className="flex items-center space-x-4">
              <img
                src={qrCodeUrl}
                alt="Booking Page QR Code"
                className="w-24 h-24 border border-gray-200 rounded"
              />
              <div>
                <p className="text-sm text-gray-600 mb-2">
                  Clients can scan this QR code to access your booking page
                </p>
                <a
                  href={qrCodeUrl}
                  download="booking-qr-code.png"
                  className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200 transition-colors"
                >
                  <FiQrCode className="h-4 w-4 mr-1" />
                  Download QR Code
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Basic Settings */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Basic Settings
        </h2>
        
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Page Title
            </label>
            <input
              type="text"
              id="title"
              value={settings.title}
              onChange={(e) => setSettings(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Book an Appointment"
            />
          </div>

          <div>
            <label htmlFor="primary_color" className="block text-sm font-medium text-gray-700 mb-2">
              Primary Color
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                id="primary_color"
                value={settings.primary_color}
                onChange={(e) => setSettings(prev => ({ ...prev, primary_color: e.target.value }))}
                className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={settings.primary_color}
                onChange={(e) => setSettings(prev => ({ ...prev, primary_color: e.target.value }))}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="#3b82f6"
              />
            </div>
          </div>

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              value={settings.description}
              onChange={(e) => setSettings(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Welcome to our booking page. Select a service and time that works for you."
            />
          </div>
        </div>
      </div>

      {/* Display Options */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Display Options
        </h2>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Show Logo</h3>
              <p className="text-sm text-gray-500">Display your business logo on the booking page</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.show_logo}
                onChange={(e) => setSettings(prev => ({ ...prev, show_logo: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Show Prices</h3>
              <p className="text-sm text-gray-500">Display service prices on the booking page</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.show_prices}
                onChange={(e) => setSettings(prev => ({ ...prev, show_prices: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Show Duration</h3>
              <p className="text-sm text-gray-500">Display service duration on the booking page</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.show_duration}
                onChange={(e) => setSettings(prev => ({ ...prev, show_duration: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Client Requirements */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Client Information Requirements
        </h2>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Require Phone Number</h3>
              <p className="text-sm text-gray-500">Make phone number a required field</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.require_phone}
                onChange={(e) => setSettings(prev => ({ ...prev, require_phone: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Require Address</h3>
              <p className="text-sm text-gray-500">Make address a required field</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.require_address}
                onChange={(e) => setSettings(prev => ({ ...prev, require_address: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Cancellation Policy */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Cancellation Policy
        </h2>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Allow Cancellations</h3>
              <p className="text-sm text-gray-500">Allow clients to cancel their appointments</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.allow_cancellation}
                onChange={(e) => setSettings(prev => ({ ...prev, allow_cancellation: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {settings.allow_cancellation && (
            <div>
              <label htmlFor="cancellation_period" className="block text-sm font-medium text-gray-700 mb-2">
                Cancellation Period (hours before appointment)
              </label>
              <input
                type="number"
                id="cancellation_period"
                value={settings.cancellation_period_hours}
                onChange={(e) => setSettings(prev => ({ ...prev, cancellation_period_hours: parseInt(e.target.value) || 0 }))}
                min="0"
                max="168"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-sm text-gray-500 mt-1">
                Clients can cancel up to this many hours before their appointment
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Custom Confirmation Message */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Custom Confirmation Message
        </h2>

        <div>
          <label htmlFor="confirmation_message" className="block text-sm font-medium text-gray-700 mb-2">
            Confirmation Message
          </label>
          <textarea
            id="confirmation_message"
            value={settings.custom_confirmation_message}
            onChange={(e) => setSettings(prev => ({ ...prev, custom_confirmation_message: e.target.value }))}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Thank you for booking with us! You will receive a confirmation email shortly."
          />
          <p className="text-sm text-gray-500 mt-1">
            This message will be shown to clients after they successfully book an appointment
          </p>
        </div>
      </div>

      {/* Advanced Customization */}
      {canUseCustomizable ? (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Advanced Customization
          </h2>

          <div className="space-y-6">
            <div>
              <label htmlFor="custom_css" className="block text-sm font-medium text-gray-700 mb-2">
                Custom CSS
              </label>
              <textarea
                id="custom_css"
                value={settings.custom_css}
                onChange={(e) => setSettings(prev => ({ ...prev, custom_css: e.target.value }))}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder="/* Add your custom CSS here */"
              />
              <p className="text-sm text-gray-500 mt-1">
                Add custom CSS to further customize the appearance of your booking page
              </p>
            </div>

            <div>
              <label htmlFor="custom_js" className="block text-sm font-medium text-gray-700 mb-2">
                Custom JavaScript
              </label>
              <textarea
                id="custom_js"
                value={settings.custom_js}
                onChange={(e) => setSettings(prev => ({ ...prev, custom_js: e.target.value }))}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder="// Add your custom JavaScript here"
              />
              <p className="text-sm text-gray-500 mt-1">
                Add custom JavaScript for advanced functionality (use with caution)
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiExternalLink className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-blue-900">
                Unlock Advanced Customization
              </h3>
              <p className="text-blue-700 mt-1">
                {upgradeMessage || 'Upgrade to Pro or Pro+ to access custom CSS, JavaScript, and advanced branding options.'}
              </p>
              <button className="mt-3 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Upgrade Now
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Save Button */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-end">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <FiSave className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save All Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BookingPageSettings;
