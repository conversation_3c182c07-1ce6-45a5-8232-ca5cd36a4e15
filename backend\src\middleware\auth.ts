import { Request, Response, NextFunction, RequestHandler } from 'express';
import { verifyToken } from '../utils/jwt';
import { UserRole } from '../models/supabase';
import { wrapMiddleware } from '../utils/routeHandler';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        tenantId: string | null;
        role: UserRole;
      };
    }
  }
}

// Authentication middleware implementation
const authenticateImpl = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Extract token
    const token = authHeader.split(' ')[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded) {
      return res.status(401).json({ message: 'Invalid or expired token' });
    }

    // Set user in request
    req.user = decoded;

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ message: 'Authentication failed' });
  }
};

// Wrapped authentication middleware for type safety
export const authenticate: RequestHandler = wrapMiddleware(authenticateImpl);

// Role-based authorization middleware
export const authorize = (...roles: UserRole[]): RequestHandler => {
  const middlewareFunction = (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    next();
  };

  return wrapMiddleware(middlewareFunction);
};

// Tenant access middleware implementation
const checkTenantAccessImpl = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  // Super admin has access to all tenants
  if (req.user.role === UserRole.SUPER_ADMIN) {
    return next();
  }

  // Get tenant ID from request parameters or body
  const tenantId = req.params.tenantId || req.body.tenantId;

  if (!tenantId) {
    return res.status(400).json({ message: 'Tenant ID is required' });
  }

  // Check if user has access to the tenant
  // Allow ADMIN, STAFF, and CLIENT users to access their own tenant
  if (req.user.tenantId !== tenantId) {
    return res.status(403).json({ message: 'Access to this tenant is denied' });
  }

  next();
};

// Wrapped tenant access middleware for type safety
export const checkTenantAccess: RequestHandler = wrapMiddleware(checkTenantAccessImpl);

